import { useEffect, useState } from 'react';
import { Select, SelectItem, Spinner } from '@nextui-org/react';
import { HiCloudArrowUp } from 'react-icons/hi2';
import { getBuckets, Bucket } from '../../api/bucketsApi';
import { toast } from 'react-toastify';
import './BucketSelector.css';

interface BucketSelectorProps {
  selectedBucket: string | null;
  onBucketChange: (bucketName: string | null) => void;
  disabled?: boolean;
}

const BucketSelector = ({ selectedBucket, onBucketChange, disabled = false }: BucketSelectorProps) => {
  const [buckets, setBuckets] = useState<Bucket[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBuckets = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const bucketsData = await getBuckets();
        setBuckets(bucketsData);
        
        // If no bucket is selected and there's a default bucket, select it
        if (!selectedBucket && bucketsData.length > 0) {
          const defaultBucket = bucketsData.find(bucket => bucket.default);
          if (defaultBucket) {
            onBucketChange(defaultBucket.bucketName);
          } else {
            // If no default bucket, select the first one
            onBucketChange(bucketsData[0].bucketName);
          }
        }
      } catch (error) {
        console.error('Failed to fetch buckets:', error);
        setError('Failed to load buckets');
        toast.error('Failed to load buckets');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBuckets();
  }, [selectedBucket, onBucketChange]);

  const handleSelectionChange = (value: string) => {
    if (value) {
      onBucketChange(value);
    }
  };

  if (isLoading) {
    return (
      <div className="bucket-selector-loading">
        <Spinner size="sm" />
        <span>Loading buckets...</span>
      </div>
    );
  }

  if (error || buckets.length === 0) {
    return (
      <div className="bucket-selector-error">
        <HiCloudArrowUp className="error-icon" />
        <span>{error || 'No buckets available'}</span>
      </div>
    );
  }

  return (
    <div className="">
      <Select
        label="Storage Bucket"
        placeholder="Select a bucket"
        selectedKeys={selectedBucket ? [selectedBucket] : []}
        onSelectionChange={(keys) => {
          const selectedKey = Array.from(keys)[0] as string;
          handleSelectionChange(selectedKey);
        }}

        variant="bordered"
        size="sm"
        startContent={<HiCloudArrowUp className="bucket-icon" />}
        placement="bottom-start"
        menuTrigger="focus"
        popoverProps={{
          placement: "bottom-start",
          offset: 4,
          crossOffset: 0,
        }}
        classNames={{
          base: "bucket-select-base",
          trigger: "bucket-select-trigger",
          value: "bucket-select-value",
          listbox: "bucket-select-listbox",
          popoverContent: "bucket-select-popover",
        }}
      >
        {buckets.map((bucket) => (
          <SelectItem
            key={bucket.bucketName}
            value={bucket.bucketName}
            textValue={bucket.title}
            description={bucket.bucketName}
            endContent={bucket.default ? <span className="bucket-badge">Default</span> : null}
          >
            {bucket.title}
          </SelectItem>
        ))}
      </Select>
    </div>
  );
};

export default BucketSelector;
