.bucket-selector {
  min-width: 280px;
  max-width: 320px;
}

.bucket-selector-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: #6b7280;
  font-size: 14px;
}

.bucket-selector-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: #ef4444;
  font-size: 14px;
}

.bucket-selector-error .error-icon {
  width: 16px;
  height: 16px;
}

.bucket-select-base {
  width: 100%;
}

.bucket-select-trigger {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  min-height: 44px;
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.bucket-select-trigger:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.bucket-select-trigger[data-focus="true"] {
  background: rgba(255, 255, 255, 0.12);
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.bucket-select-value {
  color: #ffffff;
}

.bucket-select-listbox {
  background: #1f2937 !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 10px !important;
  padding: 8px !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 2px !important;
}

.bucket-select-popover {
  background: #1f2937 !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 10px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
  max-height: 300px !important;
  overflow-y: auto !important;
  min-width: 280px !important;
}

.bucket-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

/* Style for NextUI SelectItem */
.bucket-select-listbox [data-slot="item"] {
  padding: 12px 16px !important;
  border-radius: 8px !important;
  margin: 2px 4px !important;
  transition: all 0.2s ease !important;
}

.bucket-select-listbox [data-slot="item"]:hover {
  background-color: rgba(59, 130, 246, 0.1) !important;
}

.bucket-select-listbox [data-slot="item"][data-selected="true"] {
  background-color: rgba(59, 130, 246, 0.2) !important;
}

/* Style for item text */
.bucket-select-listbox [data-slot="item"] [data-slot="title"] {
  font-weight: 500 !important;
  color: #ffffff !important;
  font-size: 14px !important;
}

.bucket-select-listbox [data-slot="item"] [data-slot="description"] {
  font-size: 12px !important;
  color: #9ca3af !important;
  margin-top: 2px !important;
}

.bucket-badge {
  display: inline-flex;
  align-items: center;
  background: #3b82f6;
  color: #ffffff;
  font-size: 10px;
  font-weight: 600;
  padding: 3px 8px;
  border-radius: 12px;
  white-space: nowrap;
}

/* Force dropdown positioning and layout */
.bucket-selector [data-slot="popover"] {
  position: absolute !important;
  top: 100% !important;
  left: 0 !important;
  right: auto !important;
  transform: none !important;
  margin-top: 4px !important;
}

/* Ensure dropdown content is properly positioned and vertical */
.bucket-selector [data-slot="listbox"] {
  position: relative !important;
  width: 100% !important;
  min-width: 280px !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Force vertical layout for all items */
.bucket-selector [role="listbox"] {
  display: flex !important;
  flex-direction: column !important;
  width: 100% !important;
}

.bucket-selector [role="option"] {
  display: flex !important;
  width: 100% !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: space-between !important;
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .bucket-select-trigger {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .bucket-select-listbox {
    background: #111827;
  }

  .bucket-select-popover {
    background: #111827;
  }
}
