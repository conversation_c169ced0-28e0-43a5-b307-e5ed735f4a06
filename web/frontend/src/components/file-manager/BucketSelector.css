.bucket-selector {
  min-width: 280px;
  max-width: 320px;
}

.bucket-selector-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: #6b7280;
  font-size: 14px;
}

.bucket-selector-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: #ef4444;
  font-size: 14px;
}

.bucket-selector-error .error-icon {
  width: 16px;
  height: 16px;
}

.bucket-select-base {
  width: 100%;
}

.bucket-select-trigger {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  min-height: 44px;
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.bucket-select-trigger:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.bucket-select-trigger[data-focus="true"] {
  background: rgba(255, 255, 255, 0.12);
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.bucket-select-value {
  color: #ffffff;
}

.bucket-select-listbox {
  background: #1f2937;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.bucket-select-popover {
  background: #1f2937;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  max-height: 300px;
  overflow-y: auto;
}

.bucket-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.bucket-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.bucket-option:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.bucket-title {
  font-weight: 500;
  color: #ffffff;
  font-size: 14px;
}

.bucket-name {
  font-size: 12px;
  color: #9ca3af;
}

.bucket-badge {
  display: inline-block;
  background: #3b82f6;
  color: #ffffff;
  font-size: 10px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  margin-top: 2px;
  width: fit-content;
}

/* Force dropdown positioning */
.bucket-selector [data-slot="popover"] {
  position: absolute !important;
  top: 100% !important;
  left: 0 !important;
  right: auto !important;
  transform: none !important;
  margin-top: 4px !important;
}

/* Ensure dropdown content is properly positioned */
.bucket-selector [data-slot="listbox"] {
  position: relative !important;
  width: 100% !important;
  min-width: 280px !important;
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .bucket-select-trigger {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .bucket-select-listbox {
    background: #111827;
  }

  .bucket-select-popover {
    background: #111827;
  }
}
