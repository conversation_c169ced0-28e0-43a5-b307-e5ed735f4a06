.bucket-selector {
  min-width: 250px;
  max-width: 300px;
}

.bucket-selector-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: #6b7280;
  font-size: 14px;
}

.bucket-selector-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: #ef4444;
  font-size: 14px;
}

.bucket-selector-error .error-icon {
  width: 16px;
  height: 16px;
}

.bucket-select-base {
  width: 100%;
}

.bucket-select-trigger {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
  min-height: 40px;
}

.bucket-select-trigger:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.bucket-select-trigger[data-focus="true"] {
  background: rgba(255, 255, 255, 0.08);
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.bucket-select-value {
  color: #ffffff;
}

.bucket-select-listbox {
  background: #1f2937;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.bucket-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.bucket-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 4px 0;
}

.bucket-title {
  font-weight: 500;
  color: #ffffff;
  font-size: 14px;
}

.bucket-name {
  font-size: 12px;
  color: #9ca3af;
}

.bucket-badge {
  display: inline-block;
  background: #3b82f6;
  color: #ffffff;
  font-size: 10px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  margin-top: 2px;
  width: fit-content;
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .bucket-select-trigger {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .bucket-select-listbox {
    background: #111827;
  }
}
