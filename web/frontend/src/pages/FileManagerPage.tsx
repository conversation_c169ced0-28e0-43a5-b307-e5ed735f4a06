import { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  HiArrowUpOnSquareStack,
  HiTrash,
  HiXMark,
  HiArrowsRightLeft,
  HiEye,
} from "react-icons/hi2";
import { getFilesByLocation, getFoldersByLocation } from "../api/filesApi";
import {
  setConvertItems,
  setLoading,
  selectFolders,
  updateFileStatus,
  selectConvertItems,
  setSelectedBucket,
  selectSelectedBucket,
} from "../redux/fileManagerSlice";
import {
  selectIsUploading,
  selectPersistModal,
  setShowModal,
  selectUploadFiles,
} from "../redux/uploadSlice";
import { store } from "../redux/store";
import LocationMenu from "../components/file-manager/LocationMenu";
import ConvertItemTable from "../components/file-manager/table/ConvertItemTable";
import BucketSelector from "../components/file-manager/BucketSelector";
import CreateFolderModal from "../components/file-manager/modal/CreateFolderModal";
import DeleteFilesModal from "../components/file-manager/modal/DeleteFilesModal";
import DeleteMultipleFilesModal from "../components/file-manager/modal/DeleteMultipleFilesModal";
import MoveFilesModal from "../components/file-manager/modal/MoveFilesModal";
import PreviewModal from "../components/file-manager/modal/PreviewModal";
import RenameFileModal from "../components/file-manager/modal/RenameFileModal";
import UploadModal from "../components/upload/UploadModal";
import { ConvertItem, FileStatusUpdate } from "../types/files";
import { toast } from "react-toastify";
import webSocketService from "../api/websocket";
import Loading from "../components/common/Loading";
import uploadService from "../services/uploadService";
import "./FileManagerPage.css";

const FileManagerPage = () => {
  const dispatch = useDispatch();
  const folders = useSelector(selectFolders);
  const isUploading = useSelector(selectIsUploading);
  const persistModal = useSelector(selectPersistModal);
  const uploadFiles = useSelector(selectUploadFiles);
  const selectedBucket = useSelector(selectSelectedBucket);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [reload, setReload] = useState<boolean>(false);
  const [location, setLocation] = useState<string>("/");

  const [isShowCreateFolderModal, setIsShowCreateFolderModal] =
    useState<boolean>(false);
  const [deletingFile, setDeletingFile] = useState<ConvertItem | null>(null);
  const [previewFile, setPreviewFile] = useState<ConvertItem | null>(null);
  const [renamingFile, setRenamingFile] = useState<ConvertItem | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<ConvertItem[]>([]);
  const [isDeletingMultiple, setIsDeletingMultiple] = useState<boolean>(false);
  const [isMovingFiles, setIsMovingFiles] = useState<boolean>(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const fetchConvertItems = async () => {
      // Don't fetch if no bucket is selected
      if (!selectedBucket) {
        console.log("FileManagerPage: No bucket selected, skipping fetch");
        return;
      }

      console.log(
        "FileManagerPage: Starting to fetch files for location:",
        location,
        "bucket:",
        selectedBucket
      );
      setIsLoading(true);
      dispatch(setLoading(true));

      try {
        const response = await getFilesByLocation(location);
        const folders = await getFoldersByLocation(location);

        console.log(
          "FileManagerPage: Fetched files:",
          response?.length || 0,
          "folders:",
          folders?.length || 0
        );

        dispatch(
          setConvertItems({
            items: response,
            folders: folders,
          })
        );
      } catch (error) {
        console.error("Error fetching files:", error);
        toast.error("Failed to load files");
      } finally {
        setIsLoading(false);
        dispatch(setLoading(false));
      }
    };

    fetchConvertItems();
  }, [location, reload, dispatch, selectedBucket]);

  // Handle modal persistence when returning to file manager during uploads
  useEffect(() => {
    if (persistModal && (isUploading || uploadFiles.length > 0)) {
      dispatch(setShowModal(true));
    }
  }, [persistModal, isUploading, dispatch]);

  // Subscribe to WebSocket file status updates
  useEffect(() => {
    // Subscribe to file status updates
    const unsubscribe = webSocketService.subscribeToFileUpdates(
      (update: FileStatusUpdate) => {
        console.log("Received file status update:", update);

        // Update the file status in the Redux store
        dispatch(
          updateFileStatus({
            id: update.id,
            status: update.status,
          })
        );

        // Find the file in the current items to get its name
        const items = selectConvertItems(store.getState());
        const file = items.find((item: ConvertItem) => item.id === update.id);
        const fileName = file
          ? file.name || file.filename
          : `File #${update.id}`;

        // Show a notification with appropriate message based on status
        switch (update.status) {
          case 0: // Success
            toast.success(`${fileName} was transcoded successfully.`);
            break;
          case 1: // Queue
            toast.info(`${fileName} is queued for transcoding.`);
            break;
          case 2: // Failed
            toast.error(`${fileName} transcoding has failed.`);
            break;
          case 3: // Transcoding
            toast.info(`${fileName} is now being transcoded.`);
            break;
          default:
            toast.info(`${fileName} status has changed.`);
        }
      }
    );

    // Cleanup on unmount
    return () => {
      unsubscribe();
    };
  }, [dispatch]);

  const handleBucketChange = (bucketName: string | null) => {
    dispatch(setSelectedBucket(bucketName));
    // Reset location to root when bucket changes
    setLocation("/");
    // Clear selected files
    setSelectedFiles([]);
  };

  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    if (!selectedBucket) {
      toast.error("Please select a bucket first");
      return;
    }

    try {
      // Show modal by default when uploading from file manager page
      await uploadService.uploadFiles(files, location, true);
      setReload(!reload);

      // Reset the file input to allow uploading the same file again
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      // Error handling is done in the upload service
      console.error("Upload failed:", error);
    }
  };

  const handleSelectFiles = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleShowUploadDetails = () => {
    dispatch(setShowModal(true));
  };

  const addFolder = (name: string) => {
    setLocation([location === "/" ? "" : location, name].join("/"));
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      await handleFileUpload(e.dataTransfer.files);
      // Clear the dataTransfer object to allow the same files to be dropped again
      e.dataTransfer.clearData();
    }
  };

  return (
    <div className="file-manager-page">
      <div className="header">
        <h1>File Manager</h1>
        <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
          <BucketSelector
            selectedBucket={selectedBucket}
            onBucketChange={handleBucketChange}
            disabled={isUploading}
          />

          <button
            onClick={handleSelectFiles}
            className="upload-button"
            disabled={isUploading || !selectedBucket}
          >
            <HiArrowUpOnSquareStack className="button-icon" />
            Upload Files
          </button>

          {(isUploading || uploadFiles.length > 0) && (
            <button
              onClick={handleShowUploadDetails}
              className="upload-detail-button"
            >
              <HiEye className="button-icon" />
              Upload Details
            </button>
          )}
        </div>
      </div>

      <input
        type="file"
        ref={fileInputRef}
        onChange={(e) => handleFileUpload(e.target.files)}
        multiple
        accept="video/*"
        style={{ display: "none" }}
      />

      {/* Main content area */}
      <div className="content-area">
        {/* Location navigation */}
        <div className="location-menu-container">
          <LocationMenu
            location={location}
            setLocation={setLocation}
            setIsShowCreateFolderModal={setIsShowCreateFolderModal}
          />
        </div>

        {/* Upload area */}
        <div className="upload-container">
          <div
            onDragOver={selectedBucket ? handleDragOver : undefined}
            onDrop={selectedBucket ? handleDrop : undefined}
            onClick={isUploading || !selectedBucket ? undefined : handleSelectFiles}
            className="upload-area"
            style={{
              opacity: isUploading || !selectedBucket ? 0.7 : 1,
              cursor: isUploading || !selectedBucket ? "default" : "pointer",
            }}
          >
            <div className="upload-content">
              <HiArrowUpOnSquareStack className="upload-icon" />
              <p className="upload-text">
                <span className="upload-highlight">
                  {!selectedBucket
                    ? "Select a bucket to upload files"
                    : isUploading
                    ? "Upload in progress..."
                    : "Click to upload"}
                </span>
                {!isUploading && selectedBucket && " or drag and drop video files"}
              </p>
              <p className="upload-subtext">
                {!selectedBucket
                  ? "Choose a storage bucket from the dropdown above"
                  : isUploading
                  ? "Please wait while files are being processed"
                  : "MP4, MOV, AVI, MKV up to 2GB"}
              </p>
            </div>
          </div>
        </div>

        {/* Action bar - only visible when files are selected */}
        {selectedFiles.length > 0 && (
          <div className="action-bar">
            <div className="selected-count">
              {selectedFiles.length} file(s) selected
            </div>
            <div className="action-bar-buttons">
              <button
                onClick={() => setSelectedFiles([])}
                className="action-bar-button cancel-button"
                title="Cancel selection"
              >
                <HiXMark className="button-icon" />
                Cancel
              </button>
              <button
                onClick={() => setIsMovingFiles(true)}
                className="action-bar-button move-button"
                title="Move selected files"
              >
                <HiArrowsRightLeft className="button-icon" />
                Move
              </button>
              <button
                onClick={() => setIsDeletingMultiple(true)}
                className="action-bar-button delete-button"
                title="Delete selected files"
              >
                <HiTrash className="button-icon" />
                Delete
              </button>
            </div>
          </div>
        )}

        {/* File table */}
        <ConvertItemTable
          isLoading={isLoading}
          location={location}
          setLocation={setLocation}
          setDeletingFile={setDeletingFile}
          setPreviewFile={setPreviewFile}
          setRenamingFile={setRenamingFile}
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
        />
      </div>

      {isLoading && !isUploading && <Loading />}

      {/* Modals */}
      <CreateFolderModal
        addFolder={addFolder}
        isOpen={isShowCreateFolderModal}
        onClose={() => setIsShowCreateFolderModal(false)}
        folders={folders}
      />

      <DeleteFilesModal
        file={deletingFile}
        isOpen={!!deletingFile}
        onClose={() => setDeletingFile(null)}
        onSuccess={() => setReload(!reload)}
      />

      <PreviewModal
        previewFile={previewFile}
        isOpen={!!previewFile}
        onClose={() => setPreviewFile(null)}
      />

      <RenameFileModal
        file={renamingFile}
        isOpen={!!renamingFile}
        onClose={() => setRenamingFile(null)}
        onSuccess={() => setReload(!reload)}
      />

      <DeleteMultipleFilesModal
        files={selectedFiles}
        isOpen={isDeletingMultiple}
        onClose={() => {
          setIsDeletingMultiple(false);
        }}
        onSuccess={() => {
          setReload(!reload);
          setSelectedFiles([]);
        }}
      />

      <MoveFilesModal
        files={selectedFiles}
        isOpen={isMovingFiles}
        currentLocation={location}
        onClose={() => {
          setIsMovingFiles(false);
        }}
        onSuccess={(destinationLocation) => {
          // If a specific destination location is provided, navigate to it
          if (destinationLocation && destinationLocation !== location) {
            // Clear selected files before changing location
            setSelectedFiles([]);
            // Navigate to the destination location
            setLocation(destinationLocation);
          } else {
            // Otherwise just reload the current location
            setReload(!reload);
            // Clear selected files
            setSelectedFiles([]);
          }
        }}
      />

      {/* Upload Modal - shows detailed upload progress */}
      <UploadModal />
    </div>
  );
};

export default FileManagerPage;
